import pandas as pd
import os

# 切换到项目根目录
os.chdir(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def parse_fixed_width_line(line, field_lengths_config):
    """
    """
    extracted_fields = []
    current_pos = 0
    sorted_field_keys = sorted(field_lengths_config.keys())

    for key in sorted_field_keys:
        length = field_lengths_config[key]
        segment = line[current_pos: current_pos + length]
        extracted_fields.append(segment)
        current_pos += length

    return extracted_fields


def process_file_to_excel(input_filepath, output_filepath, field_lengths_config):
    """
    """
    data_rows = []
    header_info = None
    footer_info = None

    try:
        with open(input_filepath, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.rstrip('\n')  # Remove newline characters

                if line.startswith("HHHH"):
                    header_info = line
                elif line.startswith("TTTT"):
                    footer_info = line
                    break
                elif header_info is not None:  # Process lines only after a header is found and before a footer
                    parsed_fields = parse_fixed_width_line(line, field_lengths_config)
                    data_rows.append(parsed_fields)
    except FileNotFoundError:
        print(f"Error: Input file '{input_filepath}' not found.")
        return
    except Exception as e:
        print(f"An error occurred during file processing: {e}")
        return

    if not data_rows:
        print("No data rows found to write to Excel.")
        if field_lengths_config:
            num_columns = len(field_lengths_config)
            column_names = [f"Field_{k}" for k in sorted(field_lengths_config.keys())]
            df = pd.DataFrame(columns=column_names)
            df.to_excel(output_filepath, index=False)
            print(f"Empty Excel file with headers created at '{output_filepath}'.")
        else:
            print("Field lengths configuration is empty, cannot determine columns for Excel.")
        return

    column_names = [f"Field_{k}" for k in sorted(field_lengths_config.keys())]
    df = pd.DataFrame(data_rows, columns=column_names)
    try:
        df.to_excel(output_filepath, index=False)
        print(f"Data successfully written to '{output_filepath}'")
    except Exception as e:
        print(f"An error occurred while writing to Excel: {e}")


if __name__ == "__main__":
    input_file = "src/IIN_data/Sample_IIN File_Full_ASCII"
    output_file = "Sample_IIN_File_Full_ASCII.xlsx"

    # input_file = "src/IIN_data/Sample_IIN File_Maintenace_ASCII"
    # output_file = "Sample_IIN_File_Maintenace_ASCII.xlsx"

    FIELD_LENGTHS_FOR_EXAMPLE_DATA = {
        1: 2,
        2: 19,
        3: 6,
        4: 25,
        5: 3,
        6: 2,
        7: 2,
        8: 1,
        9: 3,
        10: 3,
        11: 1,
        12: 2,
        13: 1,
        14: 1,
        15: 1,
        16: 8
    }

    CARD_PRODUCT_CODE  = {
        '01': 'Credit(Personal)',
        '02': 'Debit(Personal)',
        '03': 'Prepaid(Personal)',
        '04': 'Others',
        '05': 'Credit(Corporate)',
        '06': 'Debit(Corporate)',
        '07': 'Prepaid(Corporate)',
    }



    process_file_to_excel(input_file, output_file, FIELD_LENGTHS_FOR_EXAMPLE_DATA)
