import os
import pandas as pd
import traceback
from datetime import datetime
from utils.bit_parser import (
    read_bytes, to_hex_str, parse_bitmap, parse_fixed_length_field,
    parse_variable_length_field, parse_pde_fields, FIELD_LENGTHS, VARIABLE_LENGTH_FIELDS, FUNCTION_CODE_MAP
)


def parse_iso8583_message(message_bytes, message_offset=0):
    """解析ISO8583消息
    Args:
        message_bytes: 消息字节
        message_offset: 起始偏移量
    Returns:
        tuple: (解析后的字段字典, 新的偏移量)
    """
    record = {}

    # MTI
    mti_bytes, message_offset = read_bytes(message_bytes, message_offset, 4)
    record["MTI"] = mti_bytes.decode("ascii")
    print(f"字段：MTI ；值：{record['MTI']}")

    # Bitmap1
    bitmap1, message_offset = read_bytes(message_bytes, message_offset, 8)
    bitmap1_hex = to_hex_str(bitmap1)
    record["Bitmap1"] = bitmap1_hex
    print(f"字段：Bitmap1 ；值：{bitmap1_hex}")

    # Bit1 (Secondary Bitmap)
    secondary_bitmap_hex = ""
    if int(bitmap1_hex[0:2], 16) & (1 << 7):
        secondary_bitmap, message_offset = read_bytes(message_bytes, message_offset, 8)
        secondary_bitmap_hex = to_hex_str(secondary_bitmap)
        record["Bit1"] = secondary_bitmap_hex
        print(f"字段：Bit1 ；值：{secondary_bitmap_hex}")
    else:
        record["Bit1"] = ""
        print("字段：Bit1 ；值：无")

    # 获取所有存在的字段
    present_fields = parse_bitmap(bitmap1_hex, secondary_bitmap_hex)
    if 1 in present_fields:
        present_fields.remove(1)

    # 解析所有字段
    for field in present_fields:
        try:
            if field in FIELD_LENGTHS:
                # 处理固定长度字段
                value, message_offset = parse_fixed_length_field(
                    message_bytes, message_offset, FIELD_LENGTHS[field]
                )
                record[f"Bit{field}"] = value
                print(f"字段：Bit{field} ；长度：{FIELD_LENGTHS[field]}，值：{value}")

                # 特殊处理Function Code字段
                if field == 24:
                    record[f"Bit{field}"] = f"{value}:{FUNCTION_CODE_MAP.get(value, 'Unknown')}"
                    print(f"字段：Bit{field} ；值：{record[f'Bit{field}']}")

            elif field in VARIABLE_LENGTH_FIELDS:
                # 处理变长字段
                len_bytes, temp_offset = read_bytes(message_bytes, message_offset, VARIABLE_LENGTH_FIELDS[field])
                len_str = len_bytes.decode("ascii")

                try:
                    length = int(len_str)

                    # 读取内容
                    content_bytes, message_offset = read_bytes(message_bytes, temp_offset, length)
                    content = content_bytes.decode("ascii")
                    value = f"{len_str}:{content}"
                    record[f"Bit{field}"] = value
                    print(f"字段：Bit{field} ；长度：{VARIABLE_LENGTH_FIELDS[field]}，值：{value}")

                    # 特殊处理PDE字段
                    if field == 48:
                        # 存储原始长度值
                        record["Bit48"] = len_str
                        # 解析PDE内容
                        pde_fields = parse_pde_fields(content)
                        record.update(pde_fields)
                        # 打印PDE字段
                        for pde_code, pde_value in pde_fields.items():
                            print(f"字段：{pde_code} ；值：{pde_value}")
                except ValueError as e:
                    print(f"错误: 无法将长度值 '{len_str}' 转换为整数")
                    raise
        except Exception as e:
            print(f"错误: 解析字段 Bit{field} 时发生异常: {str(e)}")
            raise

    return record, message_offset


def process_iso8583_file(file_path):
    """处理ISO8583文件
    Args:
        file_path: 文件路径
    Returns:
        list: 解析后的记录列表
    """
    try:
        # 切换到项目根目录
        os.chdir(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

        # 读取文件内容
        with open(file_path, "rb") as f:
            content = f.read()

        records = []
        offset = 0
        record_index = 1

        print(f"\n{'=' * 50}")
        print(f"开始处理文件: {os.path.basename(file_path)}")
        print(f"{'=' * 50}\n")

        while offset < len(content):
            try:
                # 读取RDW
                rdw_bytes, offset = read_bytes(content, offset, 4)
                rdw = int.from_bytes(rdw_bytes, byteorder="big")

                # 读取消息
                message_bytes, offset = read_bytes(content, offset, rdw)

                print(f"\n记录 #{record_index}")
                print(f"{'-' * 30}")

                # 解析消息
                record, _ = parse_iso8583_message(message_bytes)
                record["Record#"] = record_index
                record["RDW"] = f"{rdw} x{rdw:08X}"

                records.append(record)
                record_index += 1
            except Exception as e:
                error_msg = f"处理第 {record_index} 条记录时出错:\n"
                error_msg += f"文件: {file_path}\n"
                error_msg += f"错误类型: {type(e).__name__}\n"
                error_msg += f"错误信息: {str(e)}\n"
                error_msg += "详细堆栈跟踪:\n"
                error_msg += traceback.format_exc()
                print(error_msg)
                raise

        return records
    except Exception as e:
        error_msg = f"处理文件 {file_path} 时出错:\n"
        error_msg += f"错误类型: {type(e).__name__}\n"
        error_msg += f"错误信息: {str(e)}\n"
        error_msg += "详细堆栈跟踪:\n"
        error_msg += traceback.format_exc()
        print(error_msg)
        raise


def save_to_excel(records, file_prefix):
    """保存记录到Excel文件
    Args:
        records: 记录列表
        file_prefix: 文件名前缀
    """
    df = pd.DataFrame(records)
    # 获取所有列名
    all_columns = list(df.columns)

    # 固定前缀
    prefix = ['Record#', 'RDW', 'MTI', 'Bitmap1']
    # Bit1-Bit48
    bit1_48 = [f'Bit{i}' for i in range(1, 49) if f'Bit{i}' in all_columns]
    # PDE字段
    pde_fields = sorted([col for col in all_columns if col.startswith('PDE')])
    # Bit49-Bit126
    bit49_126 = [f'Bit{i}' for i in range(49, 127) if f'Bit{i}' in all_columns]
    # 其他字段（如Bit100等特殊情况）
    others = [col for col in all_columns if col not in prefix + bit1_48 + pde_fields + bit49_126]

    # 最终列顺序
    ordered_columns = prefix + bit1_48 + pde_fields + bit49_126
    # 保证所有字段都被包含
    for col in others:
        if col not in ordered_columns:
            ordered_columns.append(col)
    # 只保留实际存在的列
    ordered_columns = [col for col in ordered_columns if col in all_columns]

    df = df.reindex(columns=ordered_columns)
    current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
    excel_path = f"src/excel/{file_prefix}_{current_time}.xlsx"
    df.to_excel(excel_path, index=False)
    print(f"Excel文件已保存到: {os.path.abspath(excel_path)}")


def main():
    # 处理不同类型的文件
    file_mappings = {
        "src/card_bin_data/AcknowledgementFile_DAY1": "AcknowledgementFile",
        "src/card_bin_data/Fee Collection_ASCII_with RDW_International": "Fee_Collection_International",
        "src/card_bin_data/File Rejection_692_ASCII_with RDW": "File_Rejection",
        "src/card_bin_data/MessageErrorInformation_ASCII_with_RDW": "MessageErrorInformation",
        "src/card_bin_data/ReconciliatinData_EAST_ASIA_with RDW": "ReconciliatinData_EAST_ASIA"
    }

    for file_path, file_prefix in file_mappings.items():
        try:
            records = process_iso8583_file(file_path)
            save_to_excel(records, file_prefix)
        except Exception as e:
            print(f"\n处理文件 {file_path} 失败，详细错误信息如上所示。\n")


if __name__ == "__main__":
    main()
