import struct
import pandas as pd
import os
import binascii
from datetime import datetime

# 切换到项目根目录
os.chdir(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 读取二进制文件内容
with open("src/card_bin_data/AcknowledgementFile_DAY1", "rb") as f:
    content = f.read()


# 辅助函数：读取 n 个字节并偏移
def read_bytes(data, offset, length):
    return data[offset:offset + length], offset + length


# 辅助函数：将 bytes 转为十六进制字符串
def to_hex_str(b):
    return b.hex().upper()


def hex_to_ascii(hex_string):
    """将十六进制字符串转换为其ASCII表示。"""
    try:
        return binascii.unhexlify(hex_string).decode('ascii')
    except binascii.Error:
        return f"Invalid Hex: {hex_string}"
    except UnicodeDecodeError:
        return f"Decode Error: {hex_string}"


def hex_to_int(hex_string):
    """将十六进制字符串转换为其整数表示。"""
    try:
        return int(hex_string, 16)
    except ValueError:
        return f"Invalid Hex for Int: {hex_string}"


# 初始化变量
offset = 0
records = []
record_index = 1

# 用于映射 Function Code 到描述
function_code_map = {
    '200': 'Presentment',
    '205': 'Representment Full',
    '280': 'Representment Partial',
    '450': 'First Chargeback Full',
    '451': 'Second Chargeback Full',
    '453': 'First Chargeback Partial',
    '454': 'Second Chargeback Partial',
    '500': 'Reconciliation Data',
    '570': 'Acknowledgement File',
    '571': 'Settlement Summary Information',
    '603': 'Retrieval Request',
    '695': 'Addendum Information',
    '689': 'File Header',
    '690': 'File Trailer',
    '691': 'Message Error Information',
    '692': 'File Rejection',
    '781': 'Fee Collection'
}

while offset < len(content):
    record = {"Record#": record_index}

    # RDW
    rdw_bytes, offset = read_bytes(content, offset, 4)
    rdw = int.from_bytes(rdw_bytes, byteorder="big")
    record["RDW"] = f"{rdw} x{rdw:08X}"

    # 读取报文数据
    message_bytes, offset = read_bytes(content, offset, rdw)
    message_offset = 0

    # MTI
    mti_bytes, message_offset = read_bytes(message_bytes, message_offset, 4)
    mti = mti_bytes.decode("ascii")
    record["MTI"] = mti

    # Bitmap1
    bitmap1, message_offset = read_bytes(message_bytes, message_offset, 8)
    bitmap1_hex = to_hex_str(bitmap1)
    record["Bitmap1"] = bitmap1_hex

    # Bit1 (Secondary Bitmap) # 检查是否有Secondary Bitmap
    if int(bitmap1_hex[0:2], 16) & (1 << 7):  # Check if the 1st bit (MSB of first byte) is set
        secondary_bitmap, message_offset = read_bytes(message_bytes, message_offset, 8)
        secondary_bitmap_hex = to_hex_str(secondary_bitmap)
        record["Bit1"] = secondary_bitmap_hex
    else:
        record["Bit1"] = "Not Present"

    # 处理 Bit24 (Function Code)
    bit24, message_offset = read_bytes(message_bytes, message_offset, 3)
    function_code = bit24.decode("ascii")
    record["Bit24"] = function_code
    record["Function Description"] = function_code_map.get(function_code, "Unknown")

    # Bit33: 先读取2位长度，然后读取对应长度的数据
    bit33_len_bytes, message_offset = read_bytes(message_bytes, message_offset, 2)  # 读取长度字段，2字节
    bit33_len = int(bit33_len_bytes.decode("ascii"))
    bit33_data, message_offset = read_bytes(message_bytes, message_offset, bit33_len)  # 根据长度读取数据
    record["Bit33_Length"] = bit33_len_bytes.decode("ascii")
    record["Bit33_Data"] = bit33_data.decode("ascii")

    # Bit48 解析
    bit48, message_offset = read_bytes(message_bytes, message_offset, 3)  # 读取长度字段，最大3字节
    bit48_code = bit48.decode("ascii")
    record["B_Bit48"] = bit48_code

    records.append(record)
    record_index += 1

# 转换为 DataFrame
df = pd.DataFrame(records)

# 生成带当前时间的文件名
current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
excel_path = f"src/excel/AcknowledgementFile_{current_time}.xlsx"

# 导出为 Excel
df.to_excel(excel_path, index=False)
print(f"Excel文件已保存到: {os.path.abspath(excel_path)}")
