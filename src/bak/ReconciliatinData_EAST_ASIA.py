import struct
import pandas as pd
import os
import binascii
from datetime import datetime

# 切换到项目根目录
os.chdir(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 读取二进制文件内容
with open("src/card_bin_data/ReconciliatinData_EAST_ASIA_with RDW", "rb") as f:
    content = f.read()


# 辅助函数：读取 n 个字节并偏移
def read_bytes(data, offset, length):
    return data[offset:offset + length], offset + length


# 辅助函数：将 bytes 转为十六进制字符串
def to_hex_str(b):
    return b.hex().upper()


def parse_bitmap(bitmap1: str, bit1: str = "") -> list:
    def hex_to_bits(hex_str):
        return bin(int(hex_str, 16))[2:].zfill(len(hex_str) * 4)

    fields = []

    # 处理 bitmap1（字段 1 到 64）
    bits1 = hex_to_bits(bitmap1)
    for i, bit in enumerate(bits1):
        if bit == '1':
            fields.append(i + 1)

    # 处理 bit1（字段 65 到 128）
    if bit1 and len(bit1) == 16:  # 8 字节 = 16 hex 字符
        bits2 = hex_to_bits(bit1)
        for i, bit in enumerate(bits2):
            if bit == '1':
                fields.append(i + 65)

    return sorted(fields)


# 初始化变量
offset = 0
records = []
record_index = 1

# 用于映射 Function Code 到描述
function_code_map = {
    '200': 'Presentment',
    '205': 'Representment Full',
    '280': 'Representment Partial',
    '450': 'First Chargeback Full',
    '451': 'Second Chargeback Full',
    '453': 'First Chargeback Partial',
    '454': 'Second Chargeback Partial',
    '500': 'Reconciliation Data',
    '570': 'Acknowledgement File',
    '571': 'Settlement Summary Information',
    '603': 'Retrieval Request',
    '695': 'Addendum Information',
    '689': 'File Header',
    '690': 'File Trailer',
    '691': 'Message Error Information',
    '692': 'File Rejection',
    '781': 'Fee Collection',
    '000000': 'Retail',
    '200000': 'Refund',
    '120000': 'Manual Cash',
    '010000': 'ATM',
    # Fee Collection还存在以下2种Processing Code
    '190000': 'Sender DEBITs to receiver',
    '290000': 'Sender CREDITs to receiver',
}

while offset < len(content):
    record = {"Record#": record_index}

    # RDW
    rdw_bytes, offset = read_bytes(content, offset, 4)
    rdw = int.from_bytes(rdw_bytes, byteorder="big")
    record["RDW"] = f"{rdw} x{rdw:08X}"

    # 读取报文数据
    message_bytes, offset = read_bytes(content, offset, rdw)
    message_offset = 0

    # MTI
    mti_bytes, message_offset = read_bytes(message_bytes, message_offset, 4)
    mti = mti_bytes.decode("ascii")
    record["MTI"] = mti

    # Bitmap1
    bitmap1, message_offset = read_bytes(message_bytes, message_offset, 8)
    bitmap1_hex = to_hex_str(bitmap1)
    record["Bitmap1"] = bitmap1_hex

    # Bit1 (Secondary Bitmap)
    if int(bitmap1_hex[0:2], 16) & (1 << 7):  # Check if the 1st bit (MSB of first byte) is set
        secondary_bitmap, message_offset = read_bytes(message_bytes, message_offset, 8)
        secondary_bitmap_hex = to_hex_str(secondary_bitmap)
        record["Bit1"] = secondary_bitmap_hex
    else:
        record["Bit1"] = ""

    # 所有存在的bit字段
    present_fields = parse_bitmap(bitmap1_hex, secondary_bitmap_hex)
    print("存在的字段编号：", present_fields)
    # 移除bit1
    present_fields.remove(1)

    # 动态字段 # 读取剩余的文件数据
    bit_offset = 0
    for field in present_fields:
        if field == 3:
            # 处理 Bit3 ：固定6位
            bit3, message_offset = read_bytes(message_bytes, message_offset, 6)
            record["Bit3"] = bit3.decode("ascii")
        elif field == 4:
            # 处理 Bit4 ：固定12位
            bit4, message_offset = read_bytes(message_bytes, message_offset, 12)
            record["Bit4"] = bit4.decode("ascii")
        elif field == 5:
            # 处理 Bit5 ：固定12位
            bit5, message_offset = read_bytes(message_bytes, message_offset, 12)
            record["Bit5"] = bit5.decode("ascii")
        elif field == 12:
            # 处理 Bit12 ：固定12位
            bit12, message_offset = read_bytes(message_bytes, message_offset, 12)
            record["Bit12"] = bit12.decode("ascii")
        elif field == 14:
            # 处理 Bit14 ：固定4位
            bit14, message_offset = read_bytes(message_bytes, message_offset, 4)
            record["Bit14"] = bit14.decode("ascii")
        elif field == 16:
            # 处理 Bit16 ：固定4位
            bit16, message_offset = read_bytes(message_bytes, message_offset, 4)
            record["Bit16"] = bit16.decode("ascii")
        elif field == 22:
            # 处理 Bit22 ：固定12位
            bit22, message_offset = read_bytes(message_bytes, message_offset, 12)
            record["Bit22"] = bit22.decode("ascii")
        elif field == 23:
            # 处理 Bit23 ：固定3位
            bit23, message_offset = read_bytes(message_bytes, message_offset, 3)
            record["Bit23"] = bit23.decode("ascii")
        elif field == 24:
            # 处理 Bit24 ：固定3位
            bit24, message_offset = read_bytes(message_bytes, message_offset, 3)
            function_code = bit24.decode("ascii")
            record["Bit24"] = function_code + ':' + function_code_map.get(function_code, "Unknown")
        elif field == 25:
            # 处理 Bit25 ：固定4位
            bit25, message_offset = read_bytes(message_bytes, message_offset, 4)
            record["Bit25"] = bit25.decode("ascii")
        elif field == 26:
            # 处理 Bit26 ：固定4位
            bit26, message_offset = read_bytes(message_bytes, message_offset, 4)
            record["Bit26"] = bit26.decode("ascii")
        elif field == 30:
            # 处理 Bit30 ：固定24位
            bit30, message_offset = read_bytes(message_bytes, message_offset, 24)
            record["Bit30"] = bit30.decode("ascii")
        elif field == 2 or field == 31:
            # 处理 Bit31 ：先读取2位长度，然后读取对应长度的内容
            bit31_len_bytes, message_offset = read_bytes(message_bytes, message_offset, 2)
            bit31_len = int(bit31_len_bytes.decode("ascii"))
            bit31_data, message_offset = read_bytes(message_bytes, message_offset, bit31_len)
            record["Bit31"] = bit31_len_bytes.decode("ascii") + ':' + bit31_data.decode("ascii")
        elif field == 37:
            # 处理 Bit37 ：固定12位
            bit37, message_offset = read_bytes(message_bytes, message_offset, 12)
            record["Bit37"] = bit37.decode("ascii")
        elif field == 38:
            # 处理 Bit38 ：固定6位
            bit38, message_offset = read_bytes(message_bytes, message_offset, 6)
            record["Bit38"] = bit38.decode("ascii")
        elif field == 40:
            # 处理 Bit40 ：固定3位
            bit40, message_offset = read_bytes(message_bytes, message_offset, 3)
            record["Bit40"] = bit40.decode("ascii")
        elif field == 41:
            # 处理 Bit41 ：固定8位
            bit41, message_offset = read_bytes(message_bytes, message_offset, 8)
            record["Bit41"] = bit41.decode("ascii")
        elif field == 42:
            # 处理 Bit42 ：固定15位
            bit42, message_offset = read_bytes(message_bytes, message_offset, 15)
            record["Bit42"] = bit42.decode("ascii")
        elif field == 43:
            # 处理 Bit43 ：先读取2位长度，然后读取对应长度的内容
            bit43_len_bytes, message_offset = read_bytes(message_bytes, message_offset, 2)
            bit43_len = int(bit43_len_bytes.decode("ascii"))
            bit43_data, message_offset = read_bytes(message_bytes, message_offset, bit43_len)
            record["Bit43"] = bit43_len_bytes.decode("ascii") + ':' + bit43_data.decode("ascii")
        elif field == 48:
            # Bit48 解析：固定3位，特殊处理，包含私有字段PDE解析
            bit48, message_offset = read_bytes(message_bytes, message_offset, 3)
            bit48_len = bit48.decode("ascii")
            record["Bit48"] = bit48_len

            # 48解析之后，要循环一下获取pde # PDE 长度动态解析，待解析总长度
            pde_len = int(bit48_len)
            pde_data, message_offset = read_bytes(message_bytes, message_offset, pde_len)
            pde_content = pde_data.decode("ascii")
            pos = 0
            # 解析PDE动态字段: 4位编号 + 3位长度 + 内容
            while pos + 7 <= len(pde_content):
                code = pde_content[pos:pos + 4]
                # 编号以30、33、39开头，且4位数字
                if not (code.isdigit() and code.startswith(('30', '33', '39'))):
                    break
                length_str = pde_content[pos + 4:pos + 7]
                if not length_str.isdigit():
                    break
                length = int(length_str)
                start = pos + 7
                end = start + length
                if end > len(pde_content):
                    break
                pde_content_str = pde_content[start:end]
                record[f'PDE{code}'] = length_str + ':' + pde_content_str
                pos = end
            pass
        elif field == 49:
            # 处理 Bit49 ：固定3位
            bit49, message_offset = read_bytes(message_bytes, message_offset, 3)
            record["Bit49"] = bit49.decode("ascii")
        elif field == 50:
            # 处理 Bit50 ：固定3位
            bit50, message_offset = read_bytes(message_bytes, message_offset, 3)
            record["Bit50"] = bit50.decode("ascii")
        elif field == 54:
            # 处理 Bit54 ：先读取3位长度，然后读取对应长度的内容
            bit54_len_bytes, message_offset = read_bytes(message_bytes, message_offset, 3)
            bit54_len = int(bit54_len_bytes.decode("ascii"))
            bit54_data, message_offset = read_bytes(message_bytes, message_offset, bit54_len)
            record["Bit54"] = bit54_len_bytes.decode("ascii") + ':' + bit54_data.decode("ascii")
        elif field == 55:
            # 处理 Bit55 ：先读取3位长度，然后读取对应长度的内容
            bit55_len_bytes, message_offset = read_bytes(message_bytes, message_offset, 3)
            bit55_len = int(bit55_len_bytes.decode("ascii"))
            bit55_data, message_offset = read_bytes(message_bytes, message_offset, bit55_len)
            record["Bit55"] = bit55_len_bytes.decode("ascii") + ':' + bit55_data.decode("ascii")
        elif field == 62 or field == 72 or (123 <= field <= 126):
            # Bit62, Bit123-126: 3位长度前缀 + 内容
            bit62_123_126_len_bytes, message_offset = read_bytes(message_bytes, message_offset, 3)
            bit62_123_126_len = int(bit62_123_126_len_bytes.decode("ascii"))
            bit62_123_126_data, message_offset = read_bytes(message_bytes, message_offset, bit62_123_126_len)
            record[f'Bit{field}'] = bit62_123_126_len_bytes.decode("ascii") + ':' + bit62_123_126_data.decode("ascii")
        elif field == 71:
            # Bit71: 固定长度8
            bit71, message_offset = read_bytes(message_bytes, message_offset, 8)
            bit71_len = bit71.decode("ascii")
            record["Bit71"] = bit71_len
        elif field == 2 or field == 32 or field == 33 or field == 100 or field == 93 or field == 94:
            # Bit32，Bit33，Bit100，Bit93，Bit94: 先读取2位长度，然后读取对应长度的数据
            bit33_bit100_len_bytes, message_offset = read_bytes(message_bytes, message_offset, 2)
            bit33_bit100_len = int(bit33_bit100_len_bytes.decode("ascii"))
            bit33_bit100_data, message_offset = read_bytes(message_bytes, message_offset, bit33_bit100_len)
            record[f'Bit{field}'] = bit33_bit100_len_bytes.decode("ascii") + ':' + bit33_bit100_data.decode("ascii")
        elif field == 97:
            # 处理 Bit97 ：固定17位
            bit97, message_offset = read_bytes(message_bytes, message_offset, 17)
            record["Bit97"] = bit97.decode("ascii")
        else:
            # 其他字段不处理
            continue

    records.append(record)
    record_index += 1

# 转换为 DataFrame
df = pd.DataFrame(records)

# 生成带当前时间的文件名
current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
excel_path = f"src/excel/ReconciliatinData_EAST_ASIA_{current_time}.xlsx"

# 导出为 Excel
df.to_excel(excel_path, index=False)
print(f"Excel文件已保存到: {os.path.abspath(excel_path)}")
